import time
from media.sensor import *
from media.display import *
from media.media import *
import math
from machine import UART
from machine import FPIOA


# ---------- 参数 ----------
LCD_W      = 800
LCD_H      = 480
A4_REAL_W  = 210.0        # mm
FOCAL_PIX  = 410.0        # 标定后填入

class Mode:
    BASIC    = 0     # 默认模式：找外/内框 + 圆/三角/四边形
    MIN_QUAD = 1     # 最小四边形模式：只找四边形，找最小
    TILT     = 2     # 倾斜模式：A4纸倾斜30-60度时测量正方形边长

current_mode = Mode.MIN_QUAD
# --------------------------

'''
    校准过程:
    把 A4 纸 正面垂直对准摄像头，用尺子量出真实距离 d_cm（例如 30 cm）。
    运行程序，记下屏幕此时外框的像素宽度 w_px（串口或直接在屏幕上看）。
    用公式反推：
    FOCAL_LENGTH_PIX = (w_px * d_cm * 10) / A4_REAL_WIDTH_MM
    例：d_cm = 30 cm 时，屏幕测得 w_px = 215 px
    FOCAL_LENGTH_PIX = 215 * 30 * 10 / 210 ≈ 307
    把计算值写回代码里的 FOCAL_LENGTH_PIX，再验证其它距离即可。
    若结果仍偏差大，可重复 1~3 步取多次平均。
'''

sensor = Sensor(width=1280, height=960)
sensor.reset()
sensor.set_framesize(width=320, height=240)
sensor.set_pixformat(Sensor.RGB565)

Display.init(Display.ST7701, width=LCD_W, height=LCD_H, to_ide=True)
MediaManager.init()
sensor.run()

fpioa = FPIOA()
fpioa.set_function(5, fpioa.UART2_TXD)
fpioa.set_function(6, fpioa.UART2_RXD)
uart = UART(UART.UART2, baudrate=115200, bits=UART.EIGHTBITS, parity=UART.PARITY_NONE, stop=UART.STOPBITS_ONE)

clk = time.clock()

def is_a4_like(rect):
    w, h = rect.w(), rect.h()
    ratio = max(w, h) / min(w, h)
    return 1.25 < ratio < 1.55


def is_a4_like_tilt(rect):
    w, h = rect.w(), rect.h()
    area = w * h
    ratio = max(w, h) / min(w, h)
    return (0.9 < ratio < 1.8) and (area > 1000)


def inside(inner, outer):
    xo, yo, wo, ho = outer.rect()
    for (x, y) in inner.corners():
        if not (xo < x < xo + wo and yo < y < yo + ho):
            return False
    return True


def dist_mm(p1, p2, current_pix_w):
    pix_dist = math.sqrt((p1[0]-p2[0])**2 + (p1[1]-p2[1])**2)
    return (pix_dist * A4_REAL_W) / current_pix_w


# -------------------------------------------------
# ROI 边界保护
# -------------------------------------------------
def clamp_roi(x, y, w, h, img_w, img_h):
    x = max(0, min(x, img_w - 1))
    y = max(0, min(y, img_h - 1))
    w = max(1, min(w, img_w - x))
    h = max(1, min(h, img_h - y))
    return (x, y, w, h)


# -------------------------------------------------
# 用 draw_line() 模拟 draw_polygon()
# -------------------------------------------------
def draw_poly(img, pts, color, thickness=2):
    n = len(pts)
    for i in range(n):
        x1, y1 = int(pts[i][0]), int(pts[i][1])
        x2, y2 = int(pts[(i+1) % n][0]), int(pts[(i+1) % n][1])
        img.draw_line(x1, y1, x2, y2, color=color, thickness=thickness)

# -------------------------------------------------
# 改进的正方形检测函数 - 处理重叠情况
# -------------------------------------------------
def is_square_like(sides, tolerance=0.25):
    """检查四边形是否接近正方形"""
    if len(sides) != 4:
        return False
    avg_side = sum(sides) / 4
    max_diff = max(abs(s - avg_side) for s in sides)
    return max_diff < avg_side * tolerance

def detect_separated_squares(img, roi, pixel_width, min_area_ratio=0.03):
    """分离重叠正方形检测算法 - 识别每个独立的正方形"""
    x0, y0, w0, h0 = roi
    gray = img.to_grayscale(roi=(x0, y0, w0, h0))

    all_squares = []
    # 使用多种阈值和形态学操作来分离重叠区域
    thresholds = [(0, 50), (0, 70), (0, 90), (10, 110), (30, 130)]

    for thresh in thresholds:
        bw = gray.binary([thresh], invert=False)
        inner_area = w0 * h0
        MIN_AREA = int(inner_area * min_area_ratio)

        # 形态学操作：先腐蚀再膨胀，分离粘连区域
        # 注意：K230可能不支持morphology，使用多次二值化代替

        # 检测blob
        blobs = bw.find_blobs([(255, 255)],
                              pixels_threshold=15,  # 更低阈值
                              area_threshold=MIN_AREA,
                              roi=(0, 0, w0, h0))

        for blob in blobs:
            corners = blob.min_corners()
            corners = [(cx + x0, cy + y0) for (cx, cy) in corners]
            blob_area = blob.pixels()

            # 计算凸包面积
            bbox_area = 0.5 * abs(sum(corners[i][0] * corners[(i+1)%4][1] -
                                      corners[(i+1)%4][0] * corners[i][1] for i in range(4)))

            if bbox_area > 0:
                area_ratio = blob_area / bbox_area

                # 适应重叠情况的面积比
                if 0.4 <= area_ratio <= 1.2:  # 放宽范围
                    sides = [dist_mm(corners[i], corners[(i+1)%4], pixel_width) for i in range(4)]

                    # 检查是否为正方形
                    if is_square_like(sides, tolerance=0.5):  # 更宽容的正方形判断
                        avg_side = sum(sides) / 4
                        center = (sum(p[0] for p in corners) / 4, sum(p[1] for p in corners) / 4)

                        # 检查是否与已有正方形重复
                        is_duplicate = False
                        for existing_corners, existing_side, existing_center in all_squares:
                            # 使用中心距离和尺寸差异来判断是否重复
                            center_dist = math.sqrt((center[0]-existing_center[0])**2 + (center[1]-existing_center[1])**2)
                            size_diff = abs(avg_side - existing_side) / max(avg_side, existing_side)

                            # 如果中心距离小且尺寸相近，认为是重复
                            if center_dist < min(avg_side, existing_side) * 0.4 and size_diff < 0.3:
                                is_duplicate = True
                                break

                        if not is_duplicate:
                            all_squares.append((corners, avg_side, center))

    # 进一步分离算法：检测可能的重叠区域
    separated_squares = []
    for corners, avg_side, center in all_squares:
        # 检查是否可能是重叠的复合形状
        if is_likely_overlapped_shape(corners, avg_side):
            # 尝试分离重叠的正方形
            sub_squares = separate_overlapped_squares(corners, avg_side, pixel_width)
            separated_squares.extend(sub_squares)
        else:
            separated_squares.append((corners, avg_side))

    # 最终去重和排序
    final_squares = remove_duplicate_squares(separated_squares)
    final_squares.sort(key=lambda x: x[1], reverse=True)  # 按边长排序

    return final_squares

def is_likely_overlapped_shape(corners, avg_side):
    """判断是否可能是重叠形状"""
    # 计算形状的复杂度
    perimeter = sum(math.sqrt((corners[i][0]-corners[(i+1)%4][0])**2 +
                             (corners[i][1]-corners[(i+1)%4][1])**2) for i in range(4))
    area = 0.5 * abs(sum(corners[i][0] * corners[(i+1)%4][1] -
                        corners[(i+1)%4][0] * corners[i][1] for i in range(4)))

    # 如果周长与面积比异常，可能是重叠形状
    expected_perimeter = 4 * avg_side * 3.779  # 像素转换系数估算
    perimeter_ratio = perimeter / expected_perimeter if expected_perimeter > 0 else 1

    return perimeter_ratio > 1.5  # 周长异常大可能是重叠

def separate_overlapped_squares(corners, avg_side, pixel_width):
    """分离重叠的正方形 - 改进版本"""
    separated = []

    # 计算原始形状的几何特征
    center_x = sum(p[0] for p in corners) / 4
    center_y = sum(p[1] for p in corners) / 4

    # 计算边长对应的像素数
    side_pixels = avg_side * pixel_width / A4_REAL_W

    # 分析形状的不规则性，推断可能的重叠模式
    edge_lengths = [math.sqrt((corners[i][0]-corners[(i+1)%4][0])**2 +
                             (corners[i][1]-corners[(i+1)%4][1])**2) for i in range(4)]
    max_edge = max(edge_lengths)
    min_edge = min(edge_lengths)

    # 如果边长差异很大，可能是重叠形状
    if max_edge / min_edge > 1.5:
        # 尝试多种分离策略
        separation_patterns = [
            # 左右分离
            [(-side_pixels*0.4, 0), (side_pixels*0.4, 0)],
            # 上下分离
            [(0, -side_pixels*0.4), (0, side_pixels*0.4)],
            # 对角分离
            [(-side_pixels*0.3, -side_pixels*0.3), (side_pixels*0.3, side_pixels*0.3)],
            # 三角分离（适用于3个重叠的情况）
            [(-side_pixels*0.3, 0), (side_pixels*0.15, -side_pixels*0.3), (side_pixels*0.15, side_pixels*0.3)]
        ]

        best_separation = []
        best_score = 0

        for pattern in separation_patterns:
            current_separation = []
            for offset_x, offset_y in pattern:
                new_center_x = center_x + offset_x
                new_center_y = center_y + offset_y

                # 生成正方形角点
                half_side = side_pixels / 2
                new_corners = [
                    (new_center_x - half_side, new_center_y - half_side),
                    (new_center_x + half_side, new_center_y - half_side),
                    (new_center_x + half_side, new_center_y + half_side),
                    (new_center_x - half_side, new_center_y + half_side)
                ]

                # 验证正方形质量
                new_sides = [dist_mm(new_corners[i], new_corners[(i+1)%4], pixel_width) for i in range(4)]
                if is_square_like(new_sides, tolerance=0.4):
                    new_avg_side = sum(new_sides) / 4
                    current_separation.append((new_corners, new_avg_side))

            # 评估分离质量（更多有效正方形 = 更好）
            if len(current_separation) > best_score:
                best_score = len(current_separation)
                best_separation = current_separation

        separated = best_separation if best_separation else [(corners, avg_side)]
    else:
        # 形状相对规则，可能不是重叠的
        separated = [(corners, avg_side)]

    return separated

def remove_duplicate_squares(squares):
    """移除重复的正方形"""
    if not squares:
        return []

    unique_squares = []
    for corners, avg_side in squares:
        center = (sum(p[0] for p in corners) / 4, sum(p[1] for p in corners) / 4)

        is_duplicate = False
        for existing_corners, existing_side in unique_squares:
            existing_center = (sum(p[0] for p in existing_corners) / 4, sum(p[1] for p in existing_corners) / 4)

            center_dist = math.sqrt((center[0]-existing_center[0])**2 + (center[1]-existing_center[1])**2)
            size_diff = abs(avg_side - existing_side) / max(avg_side, existing_side)

            if center_dist < min(avg_side, existing_side) * 0.5 and size_diff < 0.2:
                is_duplicate = True
                break

        if not is_duplicate:
            unique_squares.append((corners, avg_side))

    return unique_squares

# -------------------- 串口命令解析 -------------------------
def parse_uart():
    global current_mode
    try:
        data = uart.read()  # 直接读取数据
        if data:  # 如果有数据
            cmd = data.decode().strip()
            if cmd == 'detectminQuad':
                current_mode = Mode.MIN_QUAD
                uart.write("MODE:MIN_QUAD\n")
            elif cmd == 'basic':
                current_mode = Mode.BASIC
                uart.write("MODE:BASIC\n")
            elif cmd == 'tilt':
                current_mode = Mode.TILT
                uart.write("MODE:TILT\n")
    except:
        pass  # 忽略串口读取错误


# -------------------- 主循环 ------------------------------
while True:
    clk.tick()
    parse_uart()           # 每帧先检查串口指令
    img = sensor.snapshot()

    # ---------- BASIC 模式 ----------
    if current_mode == Mode.BASIC:
        rects = img.find_rects(threshold=10000)
        rects = sorted(rects, key=lambda r: r.w()*r.h(), reverse=True)

        outer = inner = None
        if len(rects) >= 2:
            cand = [r for r in rects[:2] if is_a4_like_tilt(r)]
            if len(cand) == 2:
                outer, inner = cand[0], cand[1]
                if not inside(inner, outer):
                    outer = inner = None

        dist_mm_a4 = 0
        if outer and inner:
            img.draw_rectangle(outer.rect(), color=(255, 0, 0), thickness=2)
            for p in outer.corners():
                img.draw_circle(p[0], p[1], 5, color=(0, 255, 0))
            img.draw_rectangle(inner.rect(), color=(0, 255, 255), thickness=2)
            for p in inner.corners():
                img.draw_circle(p[0], p[1], 5, color=(0, 0, 255))

            pixel_width = outer.w()
            dist_mm_a4  = (A4_REAL_W * FOCAL_PIX) / pixel_width
            img.draw_string(5, 5, "A4:%.1f cm" % (dist_mm_a4/10),
                            color=(255, 255, 255), scale=2)
            uart.write("D%d\n" % int(dist_mm_a4/10))
            img.draw_string(5, 200, "w_px=%d" % pixel_width,
                            color=(255, 255, 255), scale=2)

            # 圆/三角/四边形逻辑
            shrink = 5
            x0, y0, w0, h0 = inner.rect()
            x0, y0, w0, h0 = clamp_roi(x0 + shrink, y0 + shrink,
                                       w0 - 2*shrink, h0 - 2*shrink,
                                       img.width(), img.height())

            circles = img.find_circles(threshold=3000,
                                       x_margin=10, y_margin=10, r_margin=10,
                                       r_min=2, r_max=100, r_step=2,
                                       roi=(x0, y0, w0, h0))
            has_circle = False
            for c in circles:
                has_circle = True
                dia_mm = 2 * c.r() * A4_REAL_W / pixel_width
                img.draw_circle(c.x(), c.y(), c.r(), color=(0, 255, 0), thickness=2)
                img.draw_string(int(c.x()-10), int(c.y()-10),
                                "C:%.1f mm" % dia_mm,
                                color=(0, 255, 0), scale=1)
                uart.write("R%d\n" % int(dia_mm))

            if not has_circle:
                # 使用改进的分离检测算法
                detected_squares = detect_separated_squares(img, (x0, y0, w0, h0), pixel_width, min_area_ratio=0.08)

                # 如果没有检测到正方形，尝试检测三角形
                if not detected_squares:
                    gray = img.to_grayscale(roi=(x0, y0, w0, h0))
                    bw   = gray.binary([(0, 80)],invert=False)
                    inner_area = w0 * h0
                    MIN_AREA   = int(inner_area * 0.1)
                    center_x = w0 // 2
                    center_y = h0 // 2
                    blobs = sorted(
                                bw.find_blobs([(255, 255)],
                                              pixels_threshold=100,
                                              area_threshold=MIN_AREA,
                                              roi=(0, 0, w0, h0)),
                                key=lambda b: (b.cx() - center_x) ** 2 + (b.cy() - center_y) ** 2
                            )

                    for blob in blobs:
                        corners = blob.min_corners()
                        corners = [(cx + x0, cy + y0) for (cx, cy) in corners]
                        blob_area =  blob.pixels()
                        bbox_area = 0.5 * abs(sum(corners[i][0] * corners[(i+1)%4][1] -
                                                  corners[(i+1)%4][0] * corners[i][1] for i in range(4)))
                        area_ratio = blob_area / bbox_area if bbox_area > 0 else 1
                        if area_ratio < 0.9:  # 三角形检测
                            a = dist_mm(corners[0], corners[1], pixel_width)
                            b = dist_mm(corners[1], corners[2], pixel_width)
                            c = dist_mm(corners[2], corners[0], pixel_width)
                            info = "T:%.1f/%.1f/%.1f mm" % (a, b, c)
                            img.draw_string(int(blob.cx()+x0), int(blob.cy()+y0), info,
                                            color=(255, 255, 0), scale=1)
                            uart.write("T%d\n" % int(a))
                            break  # 只处理第一个三角形
                else:
                    # 显示所有分离的正方形，每个都有不同颜色
                    colors = [(255, 0, 255), (0, 255, 255), (255, 255, 0), (255, 128, 0), (128, 255, 0)]
                    for i, (corners, avg_side) in enumerate(detected_squares[:5]):  # 最多显示5个
                        sides = [dist_mm(corners[i], corners[(i+1)%4], pixel_width) for i in range(4)]
                        color = colors[i % len(colors)]
                        draw_poly(img, corners, color=color, thickness=2)

                        # 显示详细信息
                        center_x = sum(p[0] for p in corners) / 4
                        center_y = sum(p[1] for p in corners) / 4
                        info = "Q%d:%.1fmm" % (i+1, avg_side)  # 编号和平均边长
                        img.draw_string(int(center_x-20), int(center_y), info, color=color, scale=1)

                        # 发送每个正方形的数据
                        uart.write("Q%d_%d\n" % (i+1, int(avg_side)))  # 格式：Q编号_边长

                    # 在屏幕上显示总数
                    img.draw_string(5, 50, "Squares: %d" % len(detected_squares),
                                    color=(255, 255, 255), scale=2)

    elif current_mode == Mode.TILT:
        rects = img.find_rects(threshold=10000)
        rects = sorted(rects, key=lambda r: r.w()*r.h(), reverse=True)

        outer = inner = None
        if len(rects) >= 2:
            cand = [r for r in rects[:2] if is_a4_like_tilt(r)]
            if len(cand) == 2:
                outer, inner = cand[0], cand[1]
                if not inside(inner, outer):
                    outer = inner = None

        if not outer:
            Display.show_image(img,
                               x=(LCD_W - img.width())//2,
                               y=(LCD_H - img.height())//2)
            continue

        draw_poly(img, outer.corners(), color=(255, 0, 0), thickness=2)
        if inner:
            draw_poly(img, inner.corners(), color=(0, 255, 255), thickness=2)

        corners = outer.corners()
        edges = []
        for i in range(4):
            edge_length = math.sqrt((corners[i][0]-corners[(i+1)%4][0])**2 + (corners[i][1]-corners[(i+1)%4][1])**2)
            edges.append(edge_length)
        edges_sorted = sorted(edges)
        pixel_width = edges_sorted[0]
        dist_mm_a4  = (A4_REAL_W * FOCAL_PIX) / pixel_width
        img.draw_string(5, 5, "TILT %.1f cm" % (dist_mm_a4/10),
                        color=(255, 255, 255), scale=2)

        # 内框ROI - 使用外框区域作为ROI
        shrink = 5
        x0, y0, w0, h0 = outer.rect()
        x0, y0, w0, h0 = clamp_roi(x0 + shrink, y0 + shrink,
                                   w0 - 2*shrink, h0 - 2*shrink,
                                   img.width(), img.height())

        # 使用改进的分离正方形检测算法
        detected_squares = detect_separated_squares(img, (x0, y0, w0, h0), pixel_width, min_area_ratio=0.04)

        if detected_squares:
            # 显示所有分离的正方形
            colors = [(0, 255, 0), (255, 255, 0), (255, 0, 255), (0, 255, 255), (255, 128, 0)]
            for i, (square_corners, square_side) in enumerate(detected_squares[:5]):  # 最多显示5个
                color = colors[i % len(colors)]

                # 画正方形
                draw_poly(img, square_corners, color=color, thickness=3)
                center_x = sum(p[0] for p in square_corners) / 4
                center_y = sum(p[1] for p in square_corners) / 4

                # 显示编号和边长
                info = "SQ%d:%.1fmm" % (i+1, square_side)
                img.draw_string(int(center_x-25), int(center_y), info, color=color, scale=1)

                # 发送每个正方形的边长
                uart.write("SQ%d_%d\n" % (i+1, int(square_side)))

            # 显示总数和最大正方形信息
            largest_side = max(s[1] for s in detected_squares)
            img.draw_string(5, 30, "Squares:%d Max:%.1fmm" % (len(detected_squares), largest_side),
                            color=(0, 255, 0), scale=2)

    # ---------- MIN_QUAD 模式 ----------
    elif current_mode == Mode.MIN_QUAD:
        rects = img.find_rects(threshold=10000)
        rects = sorted(rects, key=lambda r: r.w()*r.h(), reverse=True)

        outer = inner = None
        if len(rects) >= 2:
            cand = [r for r in rects[:2] if is_a4_like_tilt(r)]
            if len(cand) == 2:
                outer, inner = cand[0], cand[1]
                if not inside(inner, outer):
                    outer = inner = None

        if not outer:
            Display.show_image(img,
                               x=(LCD_W - img.width())//2,
                               y=(LCD_H - img.height())//2)
            continue

        # 画外框和内框
        draw_poly(img, outer.corners(), color=(255, 0, 0), thickness=2)
        if inner:
            draw_poly(img, inner.corners(), color=(0, 255, 255), thickness=2)

        corners = outer.corners()
        edges = []
        for i in range(4):
            edge_length = math.sqrt((corners[i][0]-corners[(i+1)%4][0])**2 + (corners[i][1]-corners[(i+1)%4][1])**2)
            edges.append(edge_length)
        edges_sorted = sorted(edges)
        pixel_width = edges_sorted[0]
        dist_mm_a4  = (A4_REAL_W * FOCAL_PIX) / pixel_width
        img.draw_string(5, 5, "MIN_QUAD %.1f cm" % (dist_mm_a4/10),
                        color=(255, 255, 255), scale=2)

        # ROI
        shrink = 5
        x0, y0, w0, h0 = outer.rect()
        x0, y0, w0, h0 = clamp_roi(x0 + shrink, y0 + shrink,
                                   w0 - 2*shrink, h0 - 2*shrink,
                                   img.width(), img.height())

        # 使用改进的分离正方形检测算法
        detected_squares = detect_separated_squares(img, (x0, y0, w0, h0), pixel_width, min_area_ratio=0.03)

        # 转换为MIN_QUAD模式需要的格式并找出最小边长
        quad_list = []
        for corners, avg_side in detected_squares:
            sides = [dist_mm(corners[i], corners[(i+1)%4], pixel_width) for i in range(4)]
            min_side = min(sides)
            quad_list.append((corners, min_side, sides, avg_side))

        if quad_list:
            # 按最小边长排序
            quad_list.sort(key=lambda q: q[1])

            # 显示所有四边形，突出显示最小的
            colors = [(255, 0, 0), (255, 0, 255), (0, 255, 255), (255, 255, 0), (128, 255, 0)]

            for i, (corners, min_side, sides, avg_side) in enumerate(quad_list[:5]):
                color = colors[i % len(colors)]
                is_smallest = (i == 0)  # 第一个是最小的
                thickness = 3 if is_smallest else 2

                draw_poly(img, corners, color=color, thickness=thickness)

                # 显示详细信息
                center_x = sum(p[0] for p in corners) / 4
                center_y = sum(p[1] for p in corners) / 4
                info = "Q%d:%.1f/%.1f/%.1f/%.1f" % (i+1, sides[0], sides[1], sides[2], sides[3])
                img.draw_string(int(center_x-30), int(center_y-10), info, color=color, scale=1)

                # 显示最小边长
                min_info = "MIN:%.1fmm" % min_side
                img.draw_string(int(center_x-20), int(center_y+10), min_info, color=color, scale=1)

                # 发送数据：四边形编号_最小边长
                uart.write("M%d_%d\n" % (i+1, int(min_side)))

            # 显示全局最小边长
            global_min_side = quad_list[0][1]
            img.draw_string(5, 30, "GlobalMIN:%.1fmm Count:%d" % (global_min_side, len(quad_list)),
                            color=(255, 0, 0), scale=2)

    # 统一显示
    Display.show_image(img,
                       x=(LCD_W - img.width())//2,
                       y=(LCD_H - img.height())//2)